"""
微信机器人消息接收处理主程序
"""
import asyncio
import signal
import sys
from typing import Dict, Any
from loguru import logger

from config import settings
from framework.clients.rabbitmq_client import RabbitMQClient
from framework.core.message_processor import MessageProcessor
from framework.filters.message_filter import MessageFilter
from framework.core.group_processor import GroupProcessor
from framework.clients.http_client import HTTPClient
from utils.queue_cleaner import QueueCleaner
from framework.clients.webhook_receiver import WebhookReceiver
from framework.core.models import MessageType, MessageType_49, MessageVersion
from handlers.message_handler import MessageHandler


class WeChatBotReceiver:
    """微信机器人消息接收器"""
    
    def __init__(self):
        self.rabbitmq_client = RabbitMQClient()
        self.message_processor = MessageProcessor()
        self.message_filter = MessageFilter()
        self.group_processor = GroupProcessor()
        self.http_client = HTTPClient()
        self.queue_cleaner = QueueCleaner()
        self.webhook_receiver = WebhookReceiver()

        # 消息处理器
        self.message_handler = MessageHandler()

        self.running = False
        self.cleanup_task = None
        self.webhook_task = None
    
    async def start(self):
        """启动服务"""
        logger.info("Starting WeChat Bot Receiver...")
        
        try:
            # 启动各个组件
            await self.rabbitmq_client.connect()
            await self.http_client.start()
            await self.queue_cleaner.start()
            
            # 启动消息消费者
            await self.rabbitmq_client.start_all_consumers(self.handle_message)

            # 启动webhook接收器（如果启用）
            if settings.webhook_enabled:
                self.webhook_task = asyncio.create_task(
                    self.webhook_receiver.start(self.handle_message)
                )
                logger.info("Webhook receiver started")

            # 启动定时清理任务（可选）
            if settings.cleanup_interval_minutes > 0:
                self.cleanup_task = asyncio.create_task(
                    self.queue_cleaner.start_periodic_cleanup()
                )
            
            self.running = True
            logger.info("WeChat Bot Receiver started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start WeChat Bot Receiver: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止服务"""
        if not self.running:
            return
        
        logger.info("Stopping WeChat Bot Receiver...")
        self.running = False
        
        try:
            # 停止定时清理任务
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass

            # 停止webhook接收器
            if self.webhook_task:
                await self.webhook_receiver.stop()
                self.webhook_task.cancel()
                try:
                    await self.webhook_task
                except asyncio.CancelledError:
                    pass
            
            # 停止各个组件
            await self.rabbitmq_client.stop_all_consumers()
            await self.rabbitmq_client.disconnect()
            await self.http_client.stop()
            await self.queue_cleaner.stop()
            
            logger.info("WeChat Bot Receiver stopped")
            
        except Exception as e:
            logger.error(f"Error stopping WeChat Bot Receiver: {e}")
    
    async def handle_message(self, data: Dict[str, Any], queue_name: str, version: int):
        """处理接收到的消息"""
        try:
            # 对于版本1.5、版本2和版本3，需要先进行原始消息过滤
            if version == 1 and queue_name == "wx_msg":
                if not self.message_filter.filter_raw_message_v1_5(data):
                    return
            elif version == 2:
                if not self.message_filter.filter_raw_message_v2(data):
                    return
            elif version == 3:
                if not self.message_filter.filter_raw_message_v3(data):
                    return
            
            # 标准化消息
            message = self.message_processor.normalize_message(data, queue_name, version)
            
            # 过滤和去重
            if not self.message_filter.should_process_message(message):
                return
            
            # 处理群组消息
            if message.msg_type in [MessageType.文本, MessageType.表情]:
                message = self.group_processor.process_group_message(message)

            # 使用消息处理器处理消息
            if await self.message_handler.handle_message(message):
                logger.info(f"Message {message.msg_id} processed by handler")
                return
            return
            # 如果处理器没有处理，则转发消息（保持原有逻辑）
            if await self.http_client.forward_message_with_retry(message):
                logger.info(f"Successfully forwarded message {message.msg_id} from {queue_name}")
            else:
                logger.error(f"Failed to forward message {message.msg_id} from {queue_name}")
                
        except Exception as e:
            logger.error(f"Error handling message from {queue_name}: {e}")
            logger.debug(f"Raw message data: {data}")
            # 添加更详细的错误信息
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")


async def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <6}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        "logs/wechat_bot_receiver.log",
        level=settings.log_level,
        rotation="1 day",
        retention="7 days",
        format="{time:HH:mm:ss} | {level: <6} | {name}:{function}:{line} - {message}"
    )
    
    receiver = WeChatBotReceiver()
    
    # 设置信号处理
    def signal_handler():
        logger.info("Received shutdown signal")
        asyncio.create_task(receiver.stop())
    
    # 注册信号处理器
    for sig in [signal.SIGTERM, signal.SIGINT]:
        signal.signal(sig, lambda s, f: signal_handler())
    
    try:
        await receiver.start()
        
        # 保持运行
        while receiver.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await receiver.stop()


if __name__ == "__main__":
    asyncio.run(main())
