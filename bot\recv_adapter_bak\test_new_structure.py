#!/usr/bin/env python3
"""
测试新的文件结构和消息处理器
"""
import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from framework.core.models import StandardMessage, MessageType
from handlers.message_router import MessageRouter
from handlers.message_handler import MessageHandler
from handlers.text_handler import TextMessageHandler
from handlers.group_handler import GroupMessageHandler
from handlers.config import HandlerConfig


async def test_message_router():
    """测试消息路由器"""
    print("=== 测试消息路由器 ===")
    
    router = MessageRouter()
    
    # 测试私聊文本消息
    private_text_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123456,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="你好，这是一条私聊消息",
        push_content="",
        msg_source="",
        ver=3
    )
    
    result = await router.route_message(private_text_message)
    print(f"私聊文本消息路由结果: {result}")
    
    # 测试群组文本消息
    group_text_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123457,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="这是一条群消息",
        push_content="",
        msg_source="",
        ver=3
    )
    # 添加群组ID
    group_text_message.group_id = "12345@chatroom"
    
    result = await router.route_message(group_text_message)
    print(f"群组文本消息路由结果: {result}")
    
    # 测试图片消息
    image_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123458,
        msg_type=MessageType.图片,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="[图片]",
        push_content="",
        msg_source="",
        ver=3
    )
    
    result = await router.route_message(image_message)
    print(f"图片消息路由结果: {result}")


async def test_text_handler():
    """测试文本处理器"""
    print("\n=== 测试文本处理器 ===")
    
    handler = TextMessageHandler()
    
    # 测试命令消息
    command_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123459,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="/help",
        push_content="",
        msg_source="",
        ver=3
    )
    
    result = await handler.handle_text_message(command_message)
    print(f"命令消息处理结果: {result}")
    
    # 测试关键词消息
    keyword_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123460,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="你好",
        push_content="",
        msg_source="",
        ver=3
    )
    
    result = await handler.handle_text_message(keyword_message)
    print(f"关键词消息处理结果: {result}")


async def test_group_handler():
    """测试群组处理器"""
    print("\n=== 测试群组处理器 ===")
    
    handler = GroupMessageHandler()
    
    # 测试群组消息
    group_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123461,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="这是一条群消息",
        push_content="",
        msg_source="",
        ver=3
    )
    group_message.group_id = "12345@chatroom"
    
    result = await handler.handle_group_message(group_message)
    print(f"群组消息处理结果: {result}")
    
    # 测试@消息
    mention_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123462,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender",
        to_user_name="wxid_test123",
        content="@机器人 你好",
        push_content="",
        msg_source="",
        ver=3
    )
    group_message.group_id = "12345@chatroom"
    mention_message.at_list = ["wxid_test123"]
    
    result = await handler.handle_group_message(mention_message)
    print(f"@消息处理结果: {result}")


def test_handler_config():
    """测试处理器配置"""
    print("\n=== 测试处理器配置 ===")
    
    # 测试文本配置
    text_config = HandlerConfig.get_text_config()
    print(f"文本处理器配置: {text_config['enabled']}")
    
    # 测试群组配置
    group_config = HandlerConfig.get_group_config()
    print(f"群组处理器配置: {group_config['enabled']}")
    
    # 测试命令检查
    help_enabled = HandlerConfig.is_command_enabled('/help')
    print(f"/help命令启用状态: {help_enabled}")
    
    # 测试关键词检查
    hello_enabled = HandlerConfig.is_keyword_enabled('你好')
    print(f"'你好'关键词启用状态: {hello_enabled}")
    
    # 测试垃圾消息检查
    is_spam = HandlerConfig.is_spam_keyword('这是广告消息')
    print(f"垃圾消息检测结果: {is_spam}")


def test_file_structure():
    """测试文件结构"""
    print("\n=== 测试文件结构 ===")
    
    # 检查框架文件
    framework_files = [
        'framework/core/models.py',
        'framework/core/message_processor.py',
        'framework/core/group_processor.py',
        'framework/clients/rabbitmq_client.py',
        'framework/clients/http_client.py',
        'framework/clients/webhook_receiver.py',
        'framework/filters/message_filter.py',
        'utils/queue_cleaner.py'
    ]
    
    for file_path in framework_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
    
    # 检查处理器文件
    handler_files = [
        'handlers/message_handler.py',
        'handlers/text_handler.py',
        'handlers/group_handler.py',
        'handlers/message_router.py',
        'handlers/config.py'
    ]
    
    for file_path in handler_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")


async def main():
    """主测试函数"""
    print("开始测试新的文件结构和消息处理器...")
    
    test_file_structure()
    test_handler_config()
    await test_text_handler()
    await test_group_handler()
    await test_message_router()
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
