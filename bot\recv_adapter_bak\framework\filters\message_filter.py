"""
消息过滤和去重
"""
import time
from typing import Set, Dict, Any
from collections import deque
from loguru import logger

from config import settings
from ..core.models import StandardMessage


class MessageFilter:
    """消息过滤器"""
    
    def __init__(self):
        # 去重用的消息ID集合
        self.seen_msg_ids: Set[int] = set()
        # 维护一个有序队列来限制内存使用
        self.msg_id_queue = deque(maxlen=settings.dedup_history_size)
    
    def is_recent_message(self, timestamp: int) -> bool:
        """检查消息是否是最近的（不是历史消息）"""
        current_time = int(time.time())
        time_diff_minutes = (current_time - timestamp) / 60
        
        is_recent = time_diff_minutes <= settings.filter_history_minutes
        
        if not is_recent:
            logger.debug(f"Filtered historical message: timestamp={timestamp}, diff={time_diff_minutes:.2f}min")
        
        return is_recent
    
    def is_duplicate_message(self, msg_id: int) -> bool:
        """检查消息是否重复"""
        if msg_id in self.seen_msg_ids:
            logger.debug(f"Filtered duplicate message: msg_id={msg_id}")
            return True
        
        # 添加到已见集合
        self.seen_msg_ids.add(msg_id)
        self.msg_id_queue.append(msg_id)
        
        # 如果队列满了，移除最老的消息ID
        if len(self.msg_id_queue) >= settings.dedup_history_size:
            oldest_msg_id = self.msg_id_queue[0]
            if oldest_msg_id in self.seen_msg_ids:
                self.seen_msg_ids.remove(oldest_msg_id)
        
        return False
    
    def should_process_message(self, message: StandardMessage) -> bool:
        """判断消息是否应该被处理"""
        # 检查是否是历史消息
        if not self.is_recent_message(message.timestamp):
            return False
        
        # 检查是否重复
        if self.is_duplicate_message(message.msg_id):
            return False
        
        return True
    
    def filter_raw_message_v1_5(self, data: Dict[str, Any]) -> bool:
        """过滤版本1.5的原始消息"""
        if "AddMsgs" not in data or not data["AddMsgs"]:
            return False
        
        first_msg = data["AddMsgs"][0]
        if "create_time" not in first_msg:
            return False
        
        timestamp = first_msg["create_time"]
        return self.is_recent_message(timestamp)
    
    def filter_raw_message_v2(self, data: Dict[str, Any]) -> bool:
        """过滤版本2的原始消息"""
        # 检查是否有Data字段且不为None
        if "Data" not in data or data["Data"] is None:
            return False

        # 检查是否有AddMsgs字段且不为空
        if "AddMsgs" not in data["Data"] or not data["Data"]["AddMsgs"]:
            return False

        first_msg = data["Data"]["AddMsgs"][0]
        if "CreateTime" not in first_msg:
            return False

        timestamp = first_msg["CreateTime"]
        return self.is_recent_message(timestamp)

    def filter_raw_message_v3(self, data: Dict[str, Any]) -> bool:
        """过滤版本3的原始消息（webhook）"""
        type_name = data.get("TypeName", "")

        # 对于不同类型的webhook消息，采用不同的过滤策略
        if type_name == "AddMsg":
            # AddMsg消息需要检查CreateTime
            if "Data" not in data or data["Data"] is None:
                return False
            msg_data = data["Data"]
            if "CreateTime" not in msg_data:
                return False
            timestamp = msg_data["CreateTime"]
            return self.is_recent_message(timestamp)
        elif type_name in ["ModContacts", "DelContacts", "Offline"]:
            # 这些消息类型没有CreateTime，直接通过
            return True
        else:
            # 未知类型，尝试按AddMsg处理
            if "Data" not in data or data["Data"] is None:
                return False
            msg_data = data["Data"]
            if "CreateTime" not in msg_data:
                return True  # 没有时间戳的消息直接通过
            timestamp = msg_data["CreateTime"]
            return self.is_recent_message(timestamp)
    
    def get_stats(self) -> Dict[str, int]:
        """获取过滤器统计信息"""
        return {
            "seen_msg_ids_count": len(self.seen_msg_ids),
            "msg_id_queue_length": len(self.msg_id_queue),
            "max_history_size": settings.dedup_history_size
        }
