"""
数据模型定义
"""
from typing import Optional, List, Any, Dict
from pydantic import BaseModel
from enum import IntEnum


class MessageType(IntEnum):
    """消息类型枚举"""
    文本 = 1
    图片 = 3
    位置 = 19
    语音 = 34
    名片 = 42
    视频 = 43
    表情 = 47
    应用 = 49
    查看 = 51
    文件 = 62

class MessageType_49(IntEnum):
    """消息类型枚举"""
    链接 = 5
    文件 = 6
    小程序 = 33
    群公告 = 87
    转账 = 2000
    红包 = 2001


class MessageVersion(IntEnum):
    """消息版本"""
    V1 = 1      # wechatpadpro, wx_msg
    V1_5 = 1    # wx_msg (实际上也是1，但处理逻辑略有不同)
    V2 = 2      # wxapi
    V3 = 3      # webhook


class StandardMessage(BaseModel):
    """标准化后的消息格式"""
    msg_name: str
    msg_id: int
    msg_type: int
    timestamp: int
    is_self_message: bool
    wxid: str
    uuid: str
    from_user_name: str
    to_user_name: str
    content: str
    push_content: str
    msg_source: str
    ver: int
    
    # 群组相关字段（处理后添加）
    group_id: Optional[str] = None
    at_list: Optional[List[str]] = None
    member_count: Optional[int] = None


class RawMessageV1(BaseModel):
    """版本1原始消息格式 (wechatpadpro)"""
    msg_id: int
    msg_type: int
    create_time: int
    is_self_message: bool
    account_wxid: str
    account_uuid: str
    from_user_name: str
    to_user_name: str
    content: str
    push_content: str
    msg_source: str


class RawMessageV1_5(BaseModel):
    """版本1.5原始消息格式 (wx_msg)"""
    AddMsgs: List[Dict[str, Any]]
    userName: str
    UUID: str


class RawMessageV2(BaseModel):
    """版本2原始消息格式 (wxapi)"""
    Data: Dict[str, Any]

class RawMessageV3(BaseModel):
    """版本3原始消息格式 (GeWe API webhook)"""
    TypeName: str  # 消息类型：AddMsg, ModContacts, DelContacts, Offline
    Appid: str     # 设备ID
    Wxid: str      # 所属微信的wxid
    Data: Dict[str, Any]  # 具体的消息数据


class WebhookAddMsgData(BaseModel):
    """Webhook AddMsg消息数据"""
    MsgId: int
    FromUserName: Dict[str, str]  # {"string": "wxid"}
    ToUserName: Dict[str, str]    # {"string": "wxid"}
    MsgType: int
    Content: Dict[str, str]       # {"string": "content"}
    Status: int
    ImgStatus: int
    ImgBuf: Dict[str, Any]
    CreateTime: int
    MsgSource: str
    PushContent: str
    NewMsgId: int
    MsgSeq: int


class WebhookModContactsData(BaseModel):
    """Webhook ModContacts消息数据"""
    UserName: Dict[str, str]      # {"string": "wxid"}
    NickName: Dict[str, str]      # {"string": "nickname"}
    PyInitial: Dict[str, str]     # {"string": "initials"}
    QuanPin: Dict[str, str]       # {"string": "full_pinyin"}
    Sex: int
    ImgBuf: Dict[str, Any]
    BitMask: int
    BitVal: int
    ImgFlag: int
    Remark: Dict[str, Any]
    RemarkPyinitial: Dict[str, Any]
    RemarkQuanPin: Dict[str, Any]
    ContactType: int
    RoomInfoCount: int
    DomainList: List[Dict[str, Any]]
    ChatRoomNotify: int
    AddContactScene: int
    Province: Optional[str] = None
    City: Optional[str] = None
    Signature: Optional[str] = None
    PersonalCard: int
    HasWeiXinHdHeadImg: int
    VerifyFlag: int
    Level: int
    Source: int
    WeiboFlag: int
    AlbumStyle: int
    AlbumFlag: int
    SnsUserInfo: Dict[str, Any]
    CustomizedInfo: Dict[str, Any]
    AdditionalContactList: Dict[str, Any]
    ChatroomMaxCount: Optional[int] = None
    DeleteFlag: Optional[int] = None
    Description: Optional[str] = None
    ChatroomStatus: Optional[int] = None
    Extflag: Optional[int] = None
    ChatRoomBusinessType: Optional[int] = None
    ChatRoomOwner: Optional[str] = None


class WebhookDelContactsData(BaseModel):
    """Webhook DelContacts消息数据"""
    UserName: Dict[str, str]      # {"string": "wxid"}
    DeleteContactScen: int


class WebhookOfflineData(BaseModel):
    """Webhook Offline消息数据"""
    # Offline消息没有额外的Data字段，只有基本的Appid和Wxid


